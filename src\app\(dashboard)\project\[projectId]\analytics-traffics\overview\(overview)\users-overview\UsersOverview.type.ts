import { ProgressbarData } from "../../../types/AnalyticsTraffics.types";

// API Response Types for Countries Data
export interface CountryItem {
  name: string;
  users: number;
  percentage: number;
}

export interface CountryDataTotals {
  total_users: number;
}

export interface CountryDataPeriod {
  start_date: string;
  end_date: string;
}

export interface CountryData {
  items: CountryItem[];
  totals: CountryDataTotals;
  period: CountryDataPeriod;
}

export interface CountriesAPIResponse {
  status: string;
  project_id: string;
  metric: string;
  data: CountryData;
  last_sync: string;
}

// Transformed Data Types for Components
export interface CountryMapData {
  leftMap: Record<string, string>;
  rightMap: Record<string, string>;
}

// Legacy type for backward compatibility
export type UsersOverviewData = {
  leftMap: Record<string, string>;
  rightMap: Record<string, string>;
  progressbarData: ProgressbarData[];
};
