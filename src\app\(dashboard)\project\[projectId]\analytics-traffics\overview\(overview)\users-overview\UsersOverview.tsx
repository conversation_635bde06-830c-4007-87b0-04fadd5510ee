"use client";
import React, { useState } from "react";

/* =============================== COMPONENTS =============================== */
import Card from "@/components/ui/card";
import Title from "@/components/ui/Title";
import ChoroplethMap from "./_components/ChoroplethMap";
import DateRange from "../../../_components/date-range/DateRange";
import ProgressBar from "./_components/ProgressBar";
import Badge from "../../../analytic-insight/_components/Badge";
import { Button } from "@/components/ui/button";

/* ================================ API CALLS =============================== */
import useUsersOverview from "./UsersOverview.hook";
import { useProjectThemeColor } from "@/store/useProjectThemeColor";
import Link from "next/link";

/* ========================================================================== */
const UsersOverview = () => {
  /* ========================================================================== */
  /*                                  CONSTANTS                                 */
  /* ========================================================================== */
  const themeColor = useProjectThemeColor((state) => state.themeColor);
  const [selectedItem, setSelectedItem] = useState("Age");
  const { data: useUsersData, isLoading: useUsersIsLoading } =
    useUsersOverview(selectedItem);

  const badges = ["Countries", "Cities", "Gender", "Device", "Age"];

  /* ========================================================================== */
  /*                                   RENDER                                   */
  /* ========================================================================== */
  return (
    <Card className="overflow-hidden space-y-4">
      <Title>Users Overview</Title>
      <DateRange />
      <div className="flex flex-col lg:flex-row gap-8 lg:items-start mt-8">
        <div className="w-[100%] lg:w-[50%] h-fit md:h-[900px] lg:h-fit grid grid-cols-1 lg:grid-cols-1">
          {useUsersIsLoading && !useUsersData && (
            <>
              <ChoroplethMap
                color="#914AC4"
                countryValues={{ IRN: "0" }}
                className="animate-pulse"
              />
              <ChoroplethMap
                color="#F8BD00"
                countryValues={{ IRN: "0" }}
                className="animate-pulse"
              />
            </>
          )}
          {useUsersData && (
            <>
              <ChoroplethMap
                color="#914AC4"
                countryValues={useUsersData?.leftMap || { IRN: "0" }}
              />
              <ChoroplethMap
                color="#F8BD00"
                countryValues={useUsersData?.rightMap || { IRN: "0" }}
              />
            </>
          )}
        </div>
        <div className="w-full lg:w-[50%] space-y-6 p-4">
          <div className="flex gap-2.5 justify-between overflow-x-auto">
            {badges.map((badge, index) => (
              <Badge
                onSelect={() => setSelectedItem(badge)}
                key={index}
                className={`px-3`}
                style={
                  selectedItem === badge
                    ? {
                        backgroundColor: themeColor + "10",
                        color: themeColor,
                        borderColor: "transparent",
                      }
                    : {}
                }
              >
                {badge}
              </Badge>
            ))}
          </div>
          {useUsersData
            ? useUsersData.progressbarData.map(
                ({ title, percentage }, index) => (
                  <ProgressBar
                    key={index}
                    isLoading={false}
                    percentage={percentage}
                    title={title}
                    color={index === 0 ? "bg-[#F8BD00]" : ""}
                  />
                )
              )
            : useUsersIsLoading &&
              Array.from({ length: 7 }).map((_, index) => (
                <ProgressBar
                  key={index}
                  isLoading={true}
                  percentage={Math.floor(Math.random() * 101)}
                  title={"loading..."}
                  color={index === 0 ? "bg-[#F8BD00]" : ""}
                />
              ))}
        </div>
      </div>
      <div className="w-full flex justify-end py-5 px-3">
        <Link href={"/project/analytics-traffics/analytic-insight?tab=users"}>
          <Button className="justify-self-end" variant={"default"}>
            See All Details
          </Button>
        </Link>
      </div>
    </Card>
  );
};

export default UsersOverview;
