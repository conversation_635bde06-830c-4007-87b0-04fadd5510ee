import AXIOS from "@/lib/axios";
import { useQuery } from "@tanstack/react-query";
import type {
  UsersOverviewData,
  CountriesAPIResponse,
} from "./UsersOverview.type";
import { useProjectId } from "@/hooks/useProjectId";
import {
  transformCountriesData,
  transformToProgressBarData,
  isValidCountriesResponse,
} from "./utils/dataTransform";

const useUsersOverview = (userOverviewQuery: string) => {
  const { projectId, isValidProjectId } = useProjectId();

  return useQuery({
    queryKey: ["user-overview-data", projectId, userOverviewQuery],
    queryFn: async (): Promise<UsersOverviewData> => {
      if (!projectId) {
        throw new Error("Project ID is required");
      }

      // For countries data, use the new API endpoint
      if (userOverviewQuery.toLowerCase() === "countries") {
        const { data } = await AXIOS.get<CountriesAPIResponse>(
          "/api/dashboard/project/analytics-traffics/overview/users-overview/countries",
          {
            params: {
              projectId,
            },
          }
        );

        // Validate the response
        if (!isValidCountriesResponse(data)) {
          throw new Error("Invalid countries API response format");
        }

        // Transform the data for the components
        const mapData = transformCountriesData(data);
        const progressbarData = transformToProgressBarData(data);

        return {
          leftMap: mapData.leftMap,
          rightMap: mapData.rightMap,
          progressbarData,
        };
      }

      // Fallback to the original endpoint for other queries
      const { data } = await AXIOS.get(
        "/api/dashboard/project/analytics-traffics/overview/users-overview",
        {
          params: {
            projectId,
            userOverview: userOverviewQuery,
          },
        }
      );
      return data;
    },
    enabled: isValidProjectId && !!projectId,
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 2,
  });
};

export default useUsersOverview;
