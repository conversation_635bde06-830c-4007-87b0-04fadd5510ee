import AXIOS from "@/lib/axios";
import { useQuery } from "@tanstack/react-query";
import type {
  UsersOverviewData,
  CountriesAPIResponse,
} from "./UsersOverview.type";
import { useProjectId } from "@/hooks/useProjectId";
import {
  transformCountriesData,
  transformToProgressBarData,
  isValidCountriesResponse,
  generateMockProgressBarData,
} from "./utils/dataTransform";

const useUsersOverview = (userOverviewQuery: string) => {
  const { projectId, isValidProjectId } = useProjectId();

  return useQuery({
    queryKey: ["user-overview-data", projectId, userOverviewQuery],
    queryFn: async (): Promise<UsersOverviewData> => {
      if (!projectId) {
        throw new Error("Project ID is required");
      }

      // For testing: Use countries data for all tabs to verify ChoroplethMap rendering
      // This ensures consistent data across all tabs for testing purposes
      const { data } = await AXIOS.get<CountriesAPIResponse>(
        "/api/dashboard/project/analytics-traffics/overview/users-overview/countries",
        {
          params: {
            projectId,
          },
        }
      );

      // Validate the response
      if (!isValidCountriesResponse(data)) {
        throw new Error("Invalid countries API response format");
      }

      // Transform the data for the components
      const mapData = transformCountriesData(data);
      const progressbarData = transformToProgressBarData(data);

      // For testing: Modify progress bar data based on selected tab
      let modifiedProgressbarData = progressbarData;

      if (userOverviewQuery.toLowerCase() !== "countries") {
        // Generate different progress bar data for other tabs while keeping map data consistent
        modifiedProgressbarData =
          generateMockProgressBarData(userOverviewQuery);
      }

      return {
        leftMap: mapData.leftMap,
        rightMap: mapData.rightMap,
        progressbarData: modifiedProgressbarData,
      };
    },
    enabled: isValidProjectId && !!projectId,
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 2,
  });
};

export default useUsersOverview;
