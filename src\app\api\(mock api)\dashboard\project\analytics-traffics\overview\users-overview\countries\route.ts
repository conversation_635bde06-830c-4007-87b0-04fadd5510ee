import { NextResponse } from "next/server";
import type { CountriesAPIResponse } from "@/app/(dashboard)/project/[projectId]/analytics-traffics/overview/(overview)/users-overview/UsersOverview.type";

// Mock data matching the API response format provided by the user
const COUNTRIES_DATA: CountriesAPIResponse = {
  status: "success",
  project_id: "d8f2b06c-de6d-4181-b9d6-ff9b75c59810",
  metric: "countries",
  data: {
    items: [
      {
        name: "Iran",
        users: 12,
        percentage: 28.57142857142857,
      },
      {
        name: "Germany",
        users: 9,
        percentage: 21.428571428571427,
      },
      {
        name: "United States",
        users: 5,
        percentage: 11.904761904761903,
      },
      {
        name: "France",
        users: 4,
        percentage: 9.523809523809524,
      },
      {
        name: "United Kingdom",
        users: 4,
        percentage: 9.523809523809524,
      },
      {
        name: "Australia",
        users: 3,
        percentage: 7.142857142857142,
      },
      {
        name: "Finland",
        users: 2,
        percentage: 4.761904761904762,
      },
      {
        name: "Albania",
        users: 1,
        percentage: 2.380952380952381,
      },
      {
        name: "Canada",
        users: 1,
        percentage: 2.380952380952381,
      },
      {
        name: "India",
        users: 1,
        percentage: 2.380952380952381,
      },
    ],
    totals: {
      total_users: 42,
    },
    period: {
      start_date: "2025-06-13",
      end_date: "2025-07-13",
    },
  },
  last_sync: "2025-07-13T09:07:45.585984Z",
};

export async function GET() {
  // Simulate API delay
  await new Promise((resolve) => setTimeout(resolve, 500));
  
  return NextResponse.json(COUNTRIES_DATA);
}
